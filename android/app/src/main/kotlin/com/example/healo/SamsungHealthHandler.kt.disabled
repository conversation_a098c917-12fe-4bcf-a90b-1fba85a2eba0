package com.example.healo

import android.app.Activity
import android.util.Log
import io.flutter.plugin.common.MethodChannel
import com.samsung.android.sdk.healthdata.*
import com.samsung.android.sdk.healthdata.HealthConstants.*
import java.util.*
import java.util.concurrent.TimeUnit

class SamsungHealthHandler(private val activity: Activity) {
    private val TAG = "SamsungHealthHandler"
    private var healthDataStore: HealthDataStore? = null
    private var isConnected = false

    // Health data types we want to access
    private val readPermissions = setOf(
        PermissionKey(StepCount.HEALTH_DATA_TYPE, PermissionType.READ),
        PermissionKey(HeartRate.HEALTH_DATA_TYPE, PermissionType.READ),
        PermissionKey(Exercise.HEALTH_DATA_TYPE, PermissionType.READ),
        PermissionKey(Sleep.HEALTH_DATA_TYPE, PermissionType.READ),
        PermissionKey(SleepStage.HEALTH_DATA_TYPE, PermissionType.READ)
    )

    fun initialize(result: MethodChannel.Result) {
        try {
            Log.d(TAG, "Initializing Samsung Health SDK")

            // Initialize Samsung Health Data Service
            HealthDataService.initialize(activity)

            // Create connection listener
            val connectionListener = object : HealthDataStore.ConnectionListener {
                override fun onConnected() {
                    Log.d(TAG, "Samsung Health connected successfully")
                    isConnected = true

                    // Request permissions after connection
                    requestPermissions { permissionGranted ->
                        if (permissionGranted) {
                            result.success(true)
                        } else {
                            result.error("PERMISSION_DENIED", "Samsung Health permissions not granted", null)
                        }
                    }
                }

                override fun onConnectionFailed(error: HealthConnectionErrorResult) {
                    Log.e(TAG, "Samsung Health connection failed: ${error.errorCode}")
                    isConnected = false
                    result.error("CONNECTION_FAILED", "Failed to connect to Samsung Health", error.errorCode.toString())
                }

                override fun onDisconnected() {
                    Log.d(TAG, "Samsung Health disconnected")
                    isConnected = false
                }
            }

            // Create and connect to health data store
            healthDataStore = HealthDataStore(activity, connectionListener)
            healthDataStore?.connectService()

        } catch (e: Exception) {
            Log.e(TAG, "Error initializing Samsung Health SDK", e)
            result.error("INIT_ERROR", "Failed to initialize Samsung Health SDK", e.message)
        }
    }

    private fun requestPermissions(callback: (Boolean) -> Unit) {
        try {
            val healthDataStore = this.healthDataStore
            if (healthDataStore == null) {
                callback(false)
                return
            }

            val permissionManager = HealthPermissionManager(healthDataStore)

            permissionManager.requestPermissions(readPermissions, activity) { permissionResult ->
                val allGranted = readPermissions.all { permission ->
                    permissionResult.isPermissionAcquired(permission)
                }

                Log.d(TAG, "Samsung Health permissions granted: $allGranted")
                callback(allGranted)
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error requesting Samsung Health permissions", e)
            callback(false)
        }
    }

    fun isHealthDataAvailable(result: MethodChannel.Result) {
        try {
            Log.d(TAG, "Checking Samsung Health data availability")
            result.success(isConnected && healthDataStore != null)
        } catch (e: Exception) {
            Log.e(TAG, "Error checking Samsung Health data availability", e)
            result.error("AVAILABILITY_ERROR", "Failed to check data availability", e.message)
        }
    }

    fun fetchHealthData(result: MethodChannel.Result) {
        try {
            Log.d(TAG, "Fetching comprehensive Samsung Health data")

            if (!isConnected || healthDataStore == null) {
                result.error("NOT_CONNECTED", "Samsung Health not connected", null)
                return
            }

            val healthData = mutableMapOf<String, Any?>()
            var completedRequests = 0
            val totalRequests = 5 // steps, heart rate, calories, sleep, distance

            fun checkCompletion() {
                completedRequests++
                if (completedRequests >= totalRequests) {
                    result.success(healthData)
                }
            }

            // Fetch steps
            fetchStepsInternal { steps ->
                healthData["steps"] = steps
                checkCompletion()
            }

            // Fetch heart rate
            fetchHeartRateInternal { heartRate ->
                healthData["heartRate"] = heartRate
                checkCompletion()
            }

            // Fetch calories (from exercise data)
            fetchCaloriesInternal { calories ->
                healthData["calories"] = calories
                checkCompletion()
            }

            // Fetch distance
            fetchDistanceInternal { distance ->
                healthData["distance"] = distance
                checkCompletion()
            }

            // Fetch sleep
            fetchSleepInternal { sleepHours ->
                healthData["sleepHours"] = sleepHours
                checkCompletion()
            }

        } catch (e: Exception) {
            Log.e(TAG, "Error fetching Samsung Health data", e)
            result.error("FETCH_ERROR", "Failed to fetch health data", e.message)
        }
    }

    fun fetchStepsOnly(result: MethodChannel.Result) {
        try {
            Log.d(TAG, "Fetching Samsung Health steps")
            fetchStepsInternal { steps ->
                result.success(steps)
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error fetching Samsung Health steps", e)
            result.error("STEPS_ERROR", "Failed to fetch steps data", e.message)
        }
    }

    private fun fetchStepsInternal(callback: (Int?) -> Unit) {
        try {
            val healthDataStore = this.healthDataStore
            if (!isConnected || healthDataStore == null) {
                callback(null)
                return
            }

            val calendar = Calendar.getInstance()
            val endTime = calendar.timeInMillis

            // Set start time to beginning of today
            calendar.set(Calendar.HOUR_OF_DAY, 0)
            calendar.set(Calendar.MINUTE, 0)
            calendar.set(Calendar.SECOND, 0)
            calendar.set(Calendar.MILLISECOND, 0)
            val startTime = calendar.timeInMillis

            val request = ReadDataRequest.Builder()
                .setDataType(StepCount.HEALTH_DATA_TYPE)
                .setLocalTimeRange(StepCount.START_TIME, StepCount.TIME_OFFSET, startTime, endTime)
                .build()

            healthDataStore.readData(request) { readResult ->
                try {
                    var totalSteps = 0
                    val iterator = readResult.iterator()

                    while (iterator.hasNext()) {
                        val data = iterator.next()
                        val steps = data.getInt(StepCount.COUNT)
                        totalSteps += steps
                    }

                    Log.d(TAG, "Samsung Health steps fetched: $totalSteps")
                    callback(if (totalSteps > 0) totalSteps else null)
                } catch (e: Exception) {
                    Log.e(TAG, "Error processing steps data", e)
                    callback(null)
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error in fetchStepsInternal", e)
            callback(null)
        }
    }

    fun fetchHeartRateOnly(result: MethodChannel.Result) {
        try {
            Log.d(TAG, "Fetching Samsung Health heart rate")
            fetchHeartRateInternal { heartRate ->
                result.success(heartRate)
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error fetching Samsung Health heart rate", e)
            result.error("HEART_RATE_ERROR", "Failed to fetch heart rate data", e.message)
        }
    }

    private fun fetchHeartRateInternal(callback: (Double?) -> Unit) {
        try {
            val healthDataStore = this.healthDataStore
            if (!isConnected || healthDataStore == null) {
                callback(null)
                return
            }

            val calendar = Calendar.getInstance()
            val endTime = calendar.timeInMillis

            // Get heart rate data from last 24 hours
            calendar.add(Calendar.DAY_OF_YEAR, -1)
            val startTime = calendar.timeInMillis

            val request = ReadDataRequest.Builder()
                .setDataType(HeartRate.HEALTH_DATA_TYPE)
                .setLocalTimeRange(HeartRate.START_TIME, HeartRate.TIME_OFFSET, startTime, endTime)
                .build()

            healthDataStore.readData(request) { readResult ->
                try {
                    var latestHeartRate: Float? = null
                    var latestTime = 0L
                    val iterator = readResult.iterator()

                    while (iterator.hasNext()) {
                        val data = iterator.next()
                        val heartRate = data.getFloat(HeartRate.HEART_RATE)
                        val time = data.getLong(HeartRate.START_TIME)

                        if (time > latestTime) {
                            latestTime = time
                            latestHeartRate = heartRate
                        }
                    }

                    Log.d(TAG, "Samsung Health heart rate fetched: $latestHeartRate")
                    callback(latestHeartRate?.toDouble())
                } catch (e: Exception) {
                    Log.e(TAG, "Error processing heart rate data", e)
                    callback(null)
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error in fetchHeartRateInternal", e)
            callback(null)
        }
    }

    fun fetchCaloriesOnly(result: MethodChannel.Result) {
        try {
            Log.d(TAG, "Fetching Samsung Health calories")
            fetchCaloriesInternal { calories ->
                result.success(calories)
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error fetching Samsung Health calories", e)
            result.error("CALORIES_ERROR", "Failed to fetch calories data", e.message)
        }
    }

    private fun fetchCaloriesInternal(callback: (Double?) -> Unit) {
        try {
            val healthDataStore = this.healthDataStore
            if (!isConnected || healthDataStore == null) {
                callback(null)
                return
            }

            val calendar = Calendar.getInstance()
            val endTime = calendar.timeInMillis

            // Set start time to beginning of today
            calendar.set(Calendar.HOUR_OF_DAY, 0)
            calendar.set(Calendar.MINUTE, 0)
            calendar.set(Calendar.SECOND, 0)
            calendar.set(Calendar.MILLISECOND, 0)
            val startTime = calendar.timeInMillis

            val request = ReadDataRequest.Builder()
                .setDataType(Exercise.HEALTH_DATA_TYPE)
                .setLocalTimeRange(Exercise.START_TIME, Exercise.TIME_OFFSET, startTime, endTime)
                .build()

            healthDataStore.readData(request) { readResult ->
                try {
                    var totalCalories = 0.0
                    val iterator = readResult.iterator()

                    while (iterator.hasNext()) {
                        val data = iterator.next()
                        val calories = data.getFloat(Exercise.CALORIE)
                        totalCalories += calories
                    }

                    Log.d(TAG, "Samsung Health calories fetched: $totalCalories")
                    callback(if (totalCalories > 0) totalCalories else null)
                } catch (e: Exception) {
                    Log.e(TAG, "Error processing calories data", e)
                    callback(null)
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error in fetchCaloriesInternal", e)
            callback(null)
        }
    }

    fun fetchSleepData(result: MethodChannel.Result) {
        try {
            Log.d(TAG, "Fetching Samsung Health sleep data")
            fetchSleepInternal { sleepHours ->
                result.success(sleepHours)
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error fetching Samsung Health sleep data", e)
            result.error("SLEEP_ERROR", "Failed to fetch sleep data", e.message)
        }
    }

    private fun fetchSleepInternal(callback: (Double?) -> Unit) {
        try {
            val healthDataStore = this.healthDataStore
            if (!isConnected || healthDataStore == null) {
                callback(null)
                return
            }

            val calendar = Calendar.getInstance()
            val endTime = calendar.timeInMillis

            // Get sleep data from last 7 days to find the most recent sleep session
            calendar.add(Calendar.DAY_OF_YEAR, -7)
            val startTime = calendar.timeInMillis

            val request = ReadDataRequest.Builder()
                .setDataType(Sleep.HEALTH_DATA_TYPE)
                .setLocalTimeRange(Sleep.START_TIME, Sleep.TIME_OFFSET, startTime, endTime)
                .build()

            healthDataStore.readData(request) { readResult ->
                try {
                    var totalSleepMinutes = 0L
                    val iterator = readResult.iterator()

                    while (iterator.hasNext()) {
                        val data = iterator.next()
                        val startSleep = data.getLong(Sleep.START_TIME)
                        val endSleep = data.getLong(Sleep.END_TIME)
                        val sleepDuration = endSleep - startSleep
                        totalSleepMinutes += TimeUnit.MILLISECONDS.toMinutes(sleepDuration)
                    }

                    val sleepHours = totalSleepMinutes / 60.0
                    Log.d(TAG, "Samsung Health sleep hours fetched: $sleepHours")
                    callback(if (sleepHours > 0) sleepHours else null)
                } catch (e: Exception) {
                    Log.e(TAG, "Error processing sleep data", e)
                    callback(null)
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error in fetchSleepInternal", e)
            callback(null)
        }
    }

    private fun fetchDistanceInternal(callback: (Double?) -> Unit) {
        try {
            val healthDataStore = this.healthDataStore
            if (!isConnected || healthDataStore == null) {
                callback(null)
                return
            }

            val calendar = Calendar.getInstance()
            val endTime = calendar.timeInMillis

            // Set start time to beginning of today
            calendar.set(Calendar.HOUR_OF_DAY, 0)
            calendar.set(Calendar.MINUTE, 0)
            calendar.set(Calendar.SECOND, 0)
            calendar.set(Calendar.MILLISECOND, 0)
            val startTime = calendar.timeInMillis

            val request = ReadDataRequest.Builder()
                .setDataType(Exercise.HEALTH_DATA_TYPE)
                .setLocalTimeRange(Exercise.START_TIME, Exercise.TIME_OFFSET, startTime, endTime)
                .build()

            healthDataStore.readData(request) { readResult ->
                try {
                    var totalDistance = 0.0
                    val iterator = readResult.iterator()

                    while (iterator.hasNext()) {
                        val data = iterator.next()
                        val distance = data.getFloat(Exercise.DISTANCE)
                        totalDistance += distance
                    }

                    Log.d(TAG, "Samsung Health distance fetched: $totalDistance meters")
                    callback(if (totalDistance > 0) totalDistance else null)
                } catch (e: Exception) {
                    Log.e(TAG, "Error processing distance data", e)
                    callback(null)
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error in fetchDistanceInternal", e)
            callback(null)
        }
    }
}
